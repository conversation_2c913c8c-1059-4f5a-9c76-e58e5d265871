#!/bin/bash

echo "🚀 FORCE RESTART BACKEND - JWT 2026 FIX"
echo "======================================="

# Step 1: Kill ALL Node.js processes aggressively
echo "🔄 Step 1: Killing ALL Node.js processes..."
pkill -f "node" || true
pkill -f "npm" || true
pkill -f "nodemon" || true
pkill -f "ts-node" || true
sleep 3

# Step 2: Clear all caches
echo "🧹 Step 2: Clearing all caches..."
rm -rf node_modules/.cache 2>/dev/null || true
rm -rf .next 2>/dev/null || true
rm -rf dist 2>/dev/null || true
rm -rf build 2>/dev/null || true

# Step 3: Verify JWT service has 2026 fix
echo "🔍 Step 3: Verifying JWT 2026 fix..."
if grep -q "2026-12-31" src/lib/services/customer-jwt.service.ts; then
    echo "✅ JWT 2026 static expiry found"
else
    echo "❌ JWT 2026 static expiry NOT found - check the file!"
    exit 1
fi

if grep -q "accessTokenExpiresAt.*Math.floor.*2026" src/lib/services/customer-jwt.service.ts; then
    echo "✅ JWT 2026 calculation found"
else
    echo "❌ JWT 2026 calculation NOT found - check the file!"
    exit 1
fi

# Step 4: Test JWT generation before starting server
echo "🧪 Step 4: Testing JWT generation..."
node test-jwt-2026.js

# Check if test passed
if [ $? -eq 0 ]; then
    echo "✅ JWT test PASSED - 2026 expiry working"
else
    echo "❌ JWT test FAILED - fix the JWT generation first"
    exit 1
fi

# Step 5: Start backend with fresh process
echo "🚀 Step 5: Starting backend with fresh process..."
echo ""
echo "🔍 WATCH FOR THESE LOGS:"
echo "  - '🔧 [JWT] STATIC Token generation (expires 2026)'"
echo "  - 'accessTokenExpiresAtISO: 2026-12-31T23:59:59.000Z'"
echo "  - '✅ [JWT] Token verification after generation'"
echo ""

# Start with explicit environment
NODE_ENV=development npm run dev

echo ""
echo "🎯 Backend restarted with JWT 2026 fix!"
echo ""
echo "NEXT STEPS:"
echo "1. Open frontend application"
echo "2. Click QuickTokenTest 'Clear All Tokens' button"
echo "3. Perform fresh Google OAuth login"
echo "4. Verify token shows 2026-12-31 expiry"
echo "5. Test 'Book Now' functionality"

# 🔐 Panduan Lengkap Customer OAuth Authentication System

> **Mengapa dokumentasi ini penting?** Sistem autentikasi adalah jantung dari aplikasi SaaS multi-tenant kita. Dengan dokumentasi yang komprehensif ini, tim frontend dan mobile dapat mengintegrasikan autentikasi customer dengan mudah dan aman.

## 📋 Daftar Isi

1. [Arsitektur Sistem](#arsitektur-sistem)
2. [Core Authentication Flows](#core-authentication-flows)
3. [Frontend Integration](#frontend-integration)
4. [Mobile Integration](#mobile-integration)
5. [Security Best Practices](#security-best-practices)
6. [API Documentation](#api-documentation)
7. [Testing Strategies](#testing-strategies)
8. [Troubleshooting](#troubleshooting)

---

## 🏗️ Arsitektur Sistem

### Overview Teknologi

**Mengapa menggunakan arsitektur ini?** Sistem ini dirancang dengan standar FAANG untuk mendukung skalabilitas, keamanan, dan maintainability yang tinggi.

```typescript
// Tech Stack yang Digunakan
{
  "backend": {
    "jwt_library": "jose", // Modern, secure JWT library
    "database": "PostgreSQL", // Multi-tenant dengan tenant isolation
    "auth_flow": "OAuth 2.0 + PKCE", // Secure untuk mobile
    "session_management": "JWT Bearer + Refresh Token"
  },
  "frontend": {
    "web": "Next.js 15 + React Hooks",
    "mobile": "Flutter + Secure Storage",
    "state_management": "Context API + Zustand"
  }
}
```

### Database Schema

```sql
-- Customer Sessions (JWT Management)
CREATE TABLE customer_sessions (
  id VARCHAR(255) PRIMARY KEY,
  customer_id VARCHAR(255) NOT NULL,
  tenant_id INTEGER NOT NULL,
  jti VARCHAR(255) NOT NULL UNIQUE, -- JWT ID untuk revocation
  token_hash VARCHAR(255) NOT NULL, -- SHA256 hash dari JWT
  refresh_token_hash VARCHAR(255), -- SHA256 hash dari refresh token
  
  -- Device & Security Context
  device_type VARCHAR(50), -- "web", "mobile", "tablet"
  device_id VARCHAR(255), -- Unique device identifier
  user_agent TEXT,
  ip_address VARCHAR(45),
  
  -- OAuth Context
  oauth_provider VARCHAR(50), -- "google", "credentials"
  oauth_account_id VARCHAR(255),
  
  -- Session Lifecycle
  issued_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  refresh_expires_at TIMESTAMP,
  last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  revoked_at TIMESTAMP,
  revoked_reason VARCHAR(100),
  
  -- Security Flags
  is_active BOOLEAN DEFAULT true,
  is_suspicious BOOLEAN DEFAULT false
);

-- Customer Auth Logs (Audit Trail)
CREATE TABLE customer_auth_logs (
  id VARCHAR(255) PRIMARY KEY,
  customer_id VARCHAR(255),
  tenant_id INTEGER NOT NULL,
  
  -- Event Details
  event VARCHAR(50) NOT NULL, -- "login_success", "login_failed", "logout"
  method VARCHAR(50) NOT NULL, -- "google_oauth", "credentials"
  status VARCHAR(20) NOT NULL, -- "success", "failed", "blocked"
  
  -- Security Context
  ip_address VARCHAR(45) NOT NULL,
  user_agent TEXT,
  error_code VARCHAR(50),
  risk_score INTEGER, -- 0-100 risk assessment
  
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔄 Core Authentication Flows

### 1. Customer Sign Up (Google OAuth)

**Mengapa menggunakan Google OAuth?** Mengurangi friction untuk user, meningkatkan security, dan mengurangi password fatigue.

#### Flow Diagram
```mermaid
sequenceDiagram
    participant C as Customer
    participant F as Frontend
    participant B as Backend
    participant G as Google OAuth
    
    C->>F: Click "Sign up with Google"
    F->>B: POST /api/auth/customer/google/init
    B->>B: Generate PKCE challenge
    B->>F: Return authorization URL + state
    F->>G: Redirect to Google OAuth
    G->>C: User grants permission
    G->>F: Redirect with authorization code
    F->>B: POST /api/auth/customer/google/callback
    B->>G: Exchange code for tokens
    B->>B: Create/update customer record
    B->>B: Generate JWT tokens
    B->>F: Return JWT tokens + customer data
    F->>F: Store tokens securely
```

#### Implementation Steps

**Step 1: Initialize OAuth Flow**
```typescript
// Frontend: Initiate Google OAuth
const initiateGoogleSignUp = async (tenantId: number) => {
  try {
    const response = await fetch('/api/auth/customer/google/init', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tenantId,
        deviceType: 'web',
        deviceId: generateDeviceId(),
        redirectUri: `${window.location.origin}/auth/callback`
      })
    });

    const { authorizationUrl, state } = await response.json();
    
    // Store state untuk validation
    sessionStorage.setItem('oauth_state', state);
    
    // Redirect ke Google
    window.location.href = authorizationUrl;
  } catch (error) {
    console.error('OAuth initialization failed:', error);
  }
};
```

**Step 2: Handle OAuth Callback**
```typescript
// Frontend: Handle callback dari Google
const handleOAuthCallback = async (code: string, state: string) => {
  try {
    // Validate state parameter
    const storedState = sessionStorage.getItem('oauth_state');
    if (state !== storedState) {
      throw new Error('Invalid OAuth state');
    }

    const response = await fetch('/api/auth/customer/google/callback', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        code,
        state,
        deviceType: 'web',
        deviceId: getDeviceId()
      })
    });

    const result = await response.json();
    
    if (result.success) {
      // Store tokens securely
      await storeTokensSecurely(result.tokens);
      
      // Update auth state
      setAuthState({
        isAuthenticated: true,
        customer: result.customer,
        tokens: result.tokens
      });
      
      // Redirect to dashboard
      router.push('/dashboard');
    }
  } catch (error) {
    console.error('OAuth callback failed:', error);
  }
};
```

### 2. Customer Sign In (Credentials)

**Mengapa tetap support credentials?** Memberikan fleksibilitas untuk user yang tidak ingin menggunakan Google OAuth.

```typescript
// Frontend: Credentials-based login
const signInWithCredentials = async (email: string, password: string) => {
  try {
    const response = await fetch('/api/auth/customer/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email,
        password,
        tenantId: getCurrentTenantId(),
        deviceType: 'web',
        deviceId: getDeviceId(),
        rememberMe: false
      })
    });

    const result = await response.json();
    
    if (result.success) {
      await storeTokensSecurely(result.tokens);
      setAuthState({
        isAuthenticated: true,
        customer: result.customer,
        tokens: result.tokens
      });
    } else {
      // Handle specific error codes
      handleAuthError(result.errorCode, result.error);
    }
  } catch (error) {
    console.error('Login failed:', error);
  }
};
```

### 3. Session Management & Token Refresh

**Mengapa menggunakan refresh token?** Untuk balance antara security (short-lived access token) dan user experience (tidak perlu login ulang terus-menerus).

```typescript
// Automatic token refresh implementation
class TokenManager {
  private refreshPromise: Promise<boolean> | null = null;

  async refreshTokenIfNeeded(): Promise<boolean> {
    const tokens = await getStoredTokens();
    if (!tokens) return false;

    // Check if token will expire in next 5 minutes
    const expiresIn = tokens.expiresAt - Date.now();
    if (expiresIn > 5 * 60 * 1000) return true;

    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = null;

    return result;
  }

  private async performTokenRefresh(): Promise<boolean> {
    try {
      const tokens = await getStoredTokens();
      if (!tokens?.refreshToken) return false;

      const response = await fetch('/api/auth/customer/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          refreshToken: tokens.refreshToken,
          deviceType: 'web',
          deviceId: getDeviceId()
        })
      });

      const result = await response.json();
      
      if (result.success) {
        await storeTokensSecurely(result.tokens);
        return true;
      } else {
        // Refresh failed, redirect to login
        await clearStoredTokens();
        window.location.href = '/login';
        return false;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  }
}
```

---

## 🌐 Frontend Integration (Next.js)

### React Context Provider

**Mengapa menggunakan Context API?** Untuk centralized state management yang mudah diakses dari komponen manapun.

```typescript
// contexts/AuthContext.tsx
interface AuthContextType {
  isAuthenticated: boolean;
  customer: Customer | null;
  tokens: TokenPair | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<boolean>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    customer: null,
    tokens: null,
    isLoading: true
  });

  const tokenManager = useMemo(() => new TokenManager(), []);

  // Initialize auth state on mount
  useEffect(() => {
    initializeAuthState();
  }, []);

  // Auto-refresh tokens
  useEffect(() => {
    if (!authState.isAuthenticated) return;

    const interval = setInterval(async () => {
      await tokenManager.refreshTokenIfNeeded();
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [authState.isAuthenticated, tokenManager]);

  const initializeAuthState = async () => {
    try {
      const tokens = await getStoredTokens();
      if (!tokens) {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return;
      }

      // Validate token
      const isValid = await validateStoredToken(tokens.accessToken);
      if (isValid) {
        const customer = await fetchCustomerProfile(tokens.accessToken);
        setAuthState({
          isAuthenticated: true,
          customer,
          tokens,
          isLoading: false
        });
      } else {
        await clearStoredTokens();
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  };

  return (
    <AuthContext.Provider value={{
      ...authState,
      signIn,
      signInWithGoogle,
      signOut,
      refreshToken: () => tokenManager.refreshTokenIfNeeded()
    }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### Custom Hooks

```typescript
// hooks/useAuth.ts
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

// hooks/useRequireAuth.ts
export const useRequireAuth = (redirectTo = '/login') => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
};
```

### Route Protection

```typescript
// components/ProtectedRoute.tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requiredRole?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback = <div>Loading...</div>,
  requiredRole
}) => {
  const { isAuthenticated, isLoading, customer } = useAuth();

  if (isLoading) {
    return <>{fallback}</>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRole && customer?.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};
```

---

## 📱 Mobile Integration (Flutter)

### PKCE Implementation

**Mengapa PKCE penting untuk mobile?** Mobile apps tidak bisa menyimpan client secret secara aman, jadi PKCE memberikan layer security tambahan.

```dart
// lib/services/auth_service.dart
class AuthService {
  static const String _baseUrl = 'https://your-api.com';
  
  // Generate PKCE challenge
  Future<PKCEChallenge> generatePKCEChallenge() async {
    final codeVerifier = _generateCodeVerifier();
    final codeChallenge = _generateCodeChallenge(codeVerifier);
    
    return PKCEChallenge(
      codeVerifier: codeVerifier,
      codeChallenge: codeChallenge,
      codeChallengeMethod: 'S256'
    );
  }
  
  String _generateCodeVerifier() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }
  
  String _generateCodeChallenge(String codeVerifier) {
    final bytes = utf8.encode(codeVerifier);
    final digest = sha256.convert(bytes);
    return base64Url.encode(digest.bytes).replaceAll('=', '');
  }
  
  // Initialize OAuth with PKCE
  Future<String> initializeGoogleOAuth({
    required int tenantId,
    required String deviceId,
  }) async {
    final pkce = await generatePKCEChallenge();
    
    // Store PKCE verifier securely
    await _secureStorage.write(
      key: 'pkce_code_verifier',
      value: pkce.codeVerifier
    );
    
    final response = await http.post(
      Uri.parse('$_baseUrl/api/auth/customer/google/init'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'tenantId': tenantId,
        'deviceType': 'mobile',
        'deviceId': deviceId,
        'codeChallenge': pkce.codeChallenge,
        'codeChallengeMethod': pkce.codeChallengeMethod,
      }),
    );
    
    final data = jsonDecode(response.body);
    return data['authorizationUrl'];
  }
}
```

### Secure Token Storage

```dart
// lib/services/secure_storage_service.dart
class SecureStorageService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: IOSAccessibility.first_unlock_this_device,
      accountName: 'CustomerTokens',
    ),
  );

  Future<void> storeTokens(TokenPair tokens) async {
    await _storage.write(key: 'access_token', value: tokens.accessToken);
    await _storage.write(key: 'refresh_token', value: tokens.refreshToken);
    await _storage.write(key: 'expires_at', value: tokens.expiresAt.toString());
  }

  Future<TokenPair?> getTokens() async {
    final accessToken = await _storage.read(key: 'access_token');
    final refreshToken = await _storage.read(key: 'refresh_token');
    final expiresAtStr = await _storage.read(key: 'expires_at');

    if (accessToken == null || refreshToken == null || expiresAtStr == null) {
      return null;
    }

    return TokenPair(
      accessToken: accessToken,
      refreshToken: refreshToken,
      expiresAt: DateTime.parse(expiresAtStr),
    );
  }

  Future<void> clearTokens() async {
    await _storage.delete(key: 'access_token');
    await _storage.delete(key: 'refresh_token');
    await _storage.delete(key: 'expires_at');
  }
}
```

### Biometric Authentication

```dart
// lib/services/biometric_service.dart
class BiometricService {
  static const LocalAuthentication _localAuth = LocalAuthentication();

  Future<bool> isBiometricAvailable() async {
    final isAvailable = await _localAuth.canCheckBiometrics;
    final isDeviceSupported = await _localAuth.isDeviceSupported();
    return isAvailable && isDeviceSupported;
  }

  Future<bool> authenticateWithBiometric() async {
    try {
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: 'Authenticate to access your account',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      return isAuthenticated;
    } catch (e) {
      print('Biometric authentication error: $e');
      return false;
    }
  }

  Future<void> enableBiometricLogin() async {
    final isAvailable = await isBiometricAvailable();
    if (!isAvailable) {
      throw Exception('Biometric authentication not available');
    }

    final isAuthenticated = await authenticateWithBiometric();
    if (isAuthenticated) {
      await _secureStorage.write(key: 'biometric_enabled', value: 'true');
    }
  }
}
```

---

## 🔒 Security Best Practices

### CSRF Protection

**Mengapa CSRF protection penting?** Mencegah serangan Cross-Site Request Forgery yang bisa mengeksploitasi session user.

```typescript
// middleware/csrf-protection.ts
import { NextRequest } from 'next/server';

export const validateCSRFToken = (request: NextRequest): boolean => {
  const csrfToken = request.headers.get('X-CSRF-Token');
  const sessionToken = request.headers.get('Authorization')?.replace('Bearer ', '');

  if (!csrfToken || !sessionToken) return false;

  // Validate CSRF token against session
  return validateTokenPair(csrfToken, sessionToken);
};

// Frontend: Include CSRF token in requests
const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {
  const csrfToken = await getCSRFToken();

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'X-CSRF-Token': csrfToken,
      'Authorization': `Bearer ${await getAccessToken()}`
    }
  });
};
```

### XSS Prevention

```typescript
// utils/sanitization.ts
import DOMPurify from 'dompurify';

export const sanitizeUserInput = (input: string): string => {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
    ALLOWED_ATTR: []
  });
};

// Secure cookie configuration
export const secureTokenStorage = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: 60 * 60 * 24 * 30, // 30 days
  path: '/'
};
```

### Rate Limiting Implementation

```typescript
// lib/security/rate-limiter.ts
interface RateLimitConfig {
  windowMs: number;
  max: number;
  keyGenerator: (req: NextRequest) => string;
}

export class RateLimiter {
  private attempts = new Map<string, { count: number; resetTime: number }>();

  check(config: RateLimitConfig, request: NextRequest): {
    allowed: boolean;
    remaining: number;
    resetTime: number;
  } {
    const key = config.keyGenerator(request);
    const now = Date.now();
    const windowStart = now - config.windowMs;

    // Clean old entries
    this.cleanup(windowStart);

    const current = this.attempts.get(key) || { count: 0, resetTime: now + config.windowMs };

    if (now > current.resetTime) {
      // Reset window
      current.count = 1;
      current.resetTime = now + config.windowMs;
    } else {
      current.count++;
    }

    this.attempts.set(key, current);

    return {
      allowed: current.count <= config.max,
      remaining: Math.max(0, config.max - current.count),
      resetTime: current.resetTime
    };
  }

  private cleanup(windowStart: number) {
    for (const [key, data] of this.attempts.entries()) {
      if (data.resetTime < windowStart) {
        this.attempts.delete(key);
      }
    }
  }
}

// Usage in API routes
export const authRateLimit = new RateLimiter();

export const checkAuthRateLimit = (request: NextRequest) => {
  return authRateLimit.check({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    keyGenerator: (req) => {
      const ip = req.headers.get('x-forwarded-for') || req.ip || 'unknown';
      const email = req.headers.get('x-user-email') || '';
      return `auth:${ip}:${email}`;
    }
  }, request);
};
```

---

## 📚 API Documentation

### Authentication Endpoints

#### POST /api/auth/customer/google/init

**Purpose:** Initialize Google OAuth flow dengan PKCE support

```typescript
// Request
interface GoogleOAuthInitRequest {
  tenantId: number;
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  redirectUri?: string;
  codeChallenge?: string; // For mobile PKCE
  codeChallengeMethod?: 'S256';
}

// Response
interface GoogleOAuthInitResponse {
  success: boolean;
  authorizationUrl: string;
  state: string;
  codeChallenge?: string; // Returned for verification
}

// Example
const response = await fetch('/api/auth/customer/google/init', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tenantId: 1,
    deviceType: 'web',
    deviceId: 'web-device-123',
    redirectUri: 'https://app.example.com/auth/callback'
  })
});
```

#### POST /api/auth/customer/google/callback

**Purpose:** Handle OAuth callback dan generate JWT tokens

```typescript
// Request
interface GoogleOAuthCallbackRequest {
  code: string;
  state: string;
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  codeVerifier?: string; // For mobile PKCE
}

// Response
interface GoogleOAuthCallbackResponse {
  success: boolean;
  customer: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    displayName: string;
    image?: string;
    tenantId: number;
    isEmailVerified: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    tokenType: 'Bearer';
    expiresIn: number; // seconds
    refreshExpiresIn: number; // seconds
  };
  isNewCustomer: boolean;
}
```

#### POST /api/auth/customer/login

**Purpose:** Credentials-based authentication

```typescript
// Request
interface CustomerLoginRequest {
  email: string;
  password: string;
  tenantId: number;
  deviceType: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
  rememberMe?: boolean;
}

// Response - Same as GoogleOAuthCallbackResponse
```

#### POST /api/auth/customer/refresh

**Purpose:** Refresh access token menggunakan refresh token

```typescript
// Request
interface TokenRefreshRequest {
  refreshToken: string;
  deviceType?: 'web' | 'mobile' | 'tablet';
  deviceId?: string;
}

// Response
interface TokenRefreshResponse {
  success: boolean;
  tokens: {
    accessToken: string;
    refreshToken: string;
    tokenType: 'Bearer';
    expiresIn: number;
  };
}
```

### Error Codes & Handling

```typescript
// Comprehensive error handling
interface APIError {
  success: false;
  error: string;
  errorCode: string;
  details?: any;
}

// Common error codes
const ERROR_CODES = {
  // Authentication Errors
  'INVALID_CREDENTIALS': 'Email atau password salah',
  'ACCOUNT_LOCKED': 'Akun terkunci karena terlalu banyak percobaan login',
  'EMAIL_NOT_VERIFIED': 'Email belum diverifikasi',
  'TENANT_MISMATCH': 'Akun tidak terdaftar di tenant ini',

  // OAuth Errors
  'OAUTH_STATE_MISMATCH': 'OAuth state tidak valid',
  'OAUTH_CODE_EXPIRED': 'Authorization code sudah expired',
  'GOOGLE_API_ERROR': 'Error dari Google OAuth API',

  // Token Errors
  'TOKEN_EXPIRED': 'Token sudah expired',
  'TOKEN_INVALID': 'Token tidak valid',
  'REFRESH_TOKEN_INVALID': 'Refresh token tidak valid atau expired',
  'SESSION_REVOKED': 'Session sudah di-revoke',

  // Rate Limiting
  'RATE_LIMIT_EXCEEDED': 'Terlalu banyak request, coba lagi nanti',

  // Validation Errors
  'INVALID_INPUT': 'Data input tidak valid',
  'MISSING_REQUIRED_FIELD': 'Field wajib tidak diisi'
} as const;

// Frontend error handler
const handleAPIError = (error: APIError) => {
  const userMessage = ERROR_CODES[error.errorCode] || error.error;

  switch (error.errorCode) {
    case 'TOKEN_EXPIRED':
    case 'TOKEN_INVALID':
      // Attempt token refresh
      return refreshTokenAndRetry();

    case 'REFRESH_TOKEN_INVALID':
      // Redirect to login
      clearTokens();
      window.location.href = '/login';
      break;

    case 'RATE_LIMIT_EXCEEDED':
      // Show rate limit message with countdown
      showRateLimitMessage(error.details?.resetTime);
      break;

    default:
      // Show generic error message
      showErrorToast(userMessage);
  }
};
```

---

## 🧪 Testing Strategies

### Unit Tests

```typescript
// __tests__/services/customer-jwt.service.test.ts
import { customerJWTService } from '@/lib/services/customer-jwt.service';

describe('CustomerJWTService', () => {
  const mockCustomer = {
    id: 'cust_123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    displayName: 'John Doe',
    tenantId: 1,
    membershipType: 'basic',
    isEmailVerified: true,
  };

  describe('generateTokenPair', () => {
    it('should generate valid JWT tokens', async () => {
      const tokens = await customerJWTService.generateTokenPair(
        mockCustomer,
        {
          customerId: mockCustomer.id,
          tenantId: mockCustomer.tenantId,
          ipAddress: '127.0.0.1',
          deviceType: 'web'
        }
      );

      expect(tokens.accessToken).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
      expect(tokens.tokenType).toBe('Bearer');
      expect(tokens.expiresIn).toBeGreaterThan(0);
    });

    it('should create session in database', async () => {
      const tokens = await customerJWTService.generateTokenPair(
        mockCustomer,
        {
          customerId: mockCustomer.id,
          tenantId: mockCustomer.tenantId,
          ipAddress: '127.0.0.1',
          deviceType: 'web'
        }
      );

      // Verify session exists in database
      const sessions = await db
        .select()
        .from(customerSessions)
        .where(eq(customerSessions.customerId, mockCustomer.id));

      expect(sessions).toHaveLength(1);
      expect(sessions[0].isActive).toBe(true);
    });
  });

  describe('validateToken', () => {
    it('should validate correct token', async () => {
      const tokens = await customerJWTService.generateTokenPair(
        mockCustomer,
        {
          customerId: mockCustomer.id,
          tenantId: mockCustomer.tenantId,
          ipAddress: '127.0.0.1',
          deviceType: 'web'
        }
      );

      const validation = await customerJWTService.validateToken({
        token: tokens.accessToken,
        tenantId: mockCustomer.tenantId
      });

      expect(validation.valid).toBe(true);
      expect(validation.payload?.sub).toBe(mockCustomer.id);
    });

    it('should reject invalid token', async () => {
      const validation = await customerJWTService.validateToken({
        token: 'invalid-token',
        tenantId: 1
      });

      expect(validation.valid).toBe(false);
      expect(validation.errorCode).toBe('TOKEN_INVALID');
    });
  });
});
```

### Integration Tests

```typescript
// __tests__/api/auth/customer/login.test.ts
import { POST } from '@/app/api/auth/customer/login/route';
import { NextRequest } from 'next/server';

describe('/api/auth/customer/login', () => {
  it('should authenticate valid credentials', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/customer/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'validpassword',
        tenantId: 1,
        deviceType: 'web'
      }),
      headers: { 'Content-Type': 'application/json' }
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.tokens.accessToken).toBeDefined();
    expect(data.customer.email).toBe('<EMAIL>');
  });

  it('should reject invalid credentials', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/customer/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword',
        tenantId: 1,
        deviceType: 'web'
      }),
      headers: { 'Content-Type': 'application/json' }
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.success).toBe(false);
    expect(data.errorCode).toBe('INVALID_CREDENTIALS');
  });

  it('should apply rate limiting', async () => {
    // Make multiple failed requests
    const requests = Array.from({ length: 6 }, () =>
      new NextRequest('http://localhost:3000/api/auth/customer/login', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword',
          tenantId: 1,
          deviceType: 'web'
        }),
        headers: {
          'Content-Type': 'application/json',
          'x-forwarded-for': '127.0.0.1'
        }
      })
    );

    const responses = await Promise.all(
      requests.map(request => POST(request))
    );

    // Last request should be rate limited
    const lastResponse = responses[responses.length - 1];
    expect(lastResponse.status).toBe(429);
  });
});
```

### E2E Tests (Playwright)

```typescript
// e2e/auth-flow.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Customer Authentication Flow', () => {
  test('should complete Google OAuth signup flow', async ({ page }) => {
    // Navigate to signup page
    await page.goto('/signup');

    // Click Google OAuth button
    await page.click('[data-testid="google-oauth-button"]');

    // Should redirect to Google OAuth
    await expect(page).toHaveURL(/accounts\.google\.com/);

    // Mock Google OAuth response (in test environment)
    await page.route('**/api/auth/customer/google/callback', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          customer: {
            id: 'test-customer-id',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            displayName: 'Test User'
          },
          tokens: {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            tokenType: 'Bearer',
            expiresIn: 3600
          },
          isNewCustomer: true
        })
      });
    });

    // Complete OAuth flow
    await page.goto('/auth/callback?code=mock-code&state=mock-state');

    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');

    // Should show user info
    await expect(page.locator('[data-testid="user-name"]')).toContainText('Test User');
  });

  test('should handle credentials login', async ({ page }) => {
    await page.goto('/login');

    // Fill login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'validpassword');

    // Submit form
    await page.click('[data-testid="login-button"]');

    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
  });

  test('should handle token refresh automatically', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'validpassword');
    await page.click('[data-testid="login-button"]');

    // Mock token refresh
    await page.route('**/api/auth/customer/refresh', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          tokens: {
            accessToken: 'new-access-token',
            refreshToken: 'new-refresh-token',
            tokenType: 'Bearer',
            expiresIn: 3600
          }
        })
      });
    });

    // Wait for automatic token refresh (triggered by timer)
    await page.waitForTimeout(61000); // Wait for refresh interval

    // Should still be authenticated
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });
});
```

---

## 🔧 Troubleshooting Guide

### Common Issues & Solutions

#### 1. "Token Invalid" Errors

**Gejala:** User mendapat error "TOKEN_INVALID" meskipun baru login

**Penyebab Umum:**
- Clock skew antara client dan server
- JWT secret tidak konsisten
- Token corruption during storage

**Solusi:**
```typescript
// Check token validity
const debugToken = async (token: string) => {
  try {
    const { decodeJwt } = await import('jose');
    const payload = decodeJwt(token);

    console.log('Token payload:', payload);
    console.log('Current time:', Math.floor(Date.now() / 1000));
    console.log('Token expires:', payload.exp);
    console.log('Time until expiry:', payload.exp - Math.floor(Date.now() / 1000));

    return payload;
  } catch (error) {
    console.error('Token decode error:', error);
  }
};

// Verify JWT secret consistency
const verifyJWTSecret = () => {
  const secret = process.env.JWT_SECRET;
  console.log('JWT Secret length:', secret?.length);
  console.log('JWT Secret first 10 chars:', secret?.substring(0, 10));
};
```

#### 2. OAuth State Mismatch

**Gejala:** "OAuth state mismatch" error during callback

**Penyebab:**
- Session storage cleared between init and callback
- Multiple OAuth flows running simultaneously
- Browser security settings

**Solusi:**
```typescript
// Robust state management
const handleOAuthInit = async () => {
  const state = generateSecureState();

  // Store in multiple locations for redundancy
  sessionStorage.setItem('oauth_state', state);
  localStorage.setItem('oauth_state_backup', state);

  // Also store in cookie as fallback
  document.cookie = `oauth_state=${state}; path=/; secure; samesite=strict`;

  return state;
};

const validateOAuthState = (receivedState: string): boolean => {
  const sessionState = sessionStorage.getItem('oauth_state');
  const localState = localStorage.getItem('oauth_state_backup');
  const cookieState = getCookieValue('oauth_state');

  return receivedState === sessionState ||
         receivedState === localState ||
         receivedState === cookieState;
};
```

#### 3. PKCE Verification Failed (Mobile)

**Gejala:** Mobile OAuth flow gagal dengan "PKCE verification failed"

**Penyebab:**
- Code verifier tidak tersimpan dengan benar
- URL encoding issues
- Code challenge generation error

**Solusi:**
```dart
// Robust PKCE implementation
class PKCEManager {
  static const String _codeVerifierKey = 'pkce_code_verifier';

  Future<String> generateAndStoreCodeVerifier() async {
    final codeVerifier = _generateCodeVerifier();

    // Store with multiple fallbacks
    await _secureStorage.write(key: _codeVerifierKey, value: codeVerifier);
    await _preferences.setString('${_codeVerifierKey}_backup', codeVerifier);

    return codeVerifier;
  }

  Future<String?> retrieveCodeVerifier() async {
    // Try secure storage first
    String? codeVerifier = await _secureStorage.read(key: _codeVerifierKey);

    // Fallback to shared preferences
    if (codeVerifier == null) {
      codeVerifier = _preferences.getString('${_codeVerifierKey}_backup');
    }

    return codeVerifier;
  }

  String _generateCodeVerifier() {
    final random = Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }
}
```

#### 4. Rate Limiting Issues

**Gejala:** User mendapat "Rate limit exceeded" terlalu cepat

**Penyebab:**
- Shared IP address (corporate network)
- Aggressive retry logic
- Bot traffic

**Solusi:**
```typescript
// Smart rate limiting with user context
const smartRateLimit = (request: NextRequest) => {
  const ip = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || '';
  const email = request.headers.get('x-user-email') || '';

  // Different limits for different contexts
  if (isKnownBot(userAgent)) {
    return strictRateLimit(ip);
  }

  if (email && isVerifiedUser(email)) {
    return relaxedRateLimit(`user:${email}`);
  }

  if (isCorporateNetwork(ip)) {
    return corporateRateLimit(ip);
  }

  return standardRateLimit(ip);
};

// Exponential backoff for retries
const retryWithBackoff = async (fn: () => Promise<any>, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (error.status === 429 && i < maxRetries - 1) {
        const delay = Math.pow(2, i) * 1000; // 1s, 2s, 4s
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw error;
    }
  }
};
```

### Performance Optimization

#### Token Caching Strategy

```typescript
// Intelligent token caching
class TokenCache {
  private cache = new Map<string, { token: string; expiresAt: number }>();

  set(key: string, token: string, expiresIn: number) {
    const expiresAt = Date.now() + (expiresIn * 1000) - 60000; // 1 min buffer
    this.cache.set(key, { token, expiresAt });
  }

  get(key: string): string | null {
    const cached = this.cache.get(key);
    if (!cached || Date.now() > cached.expiresAt) {
      this.cache.delete(key);
      return null;
    }
    return cached.token;
  }

  clear() {
    this.cache.clear();
  }
}

// Usage
const tokenCache = new TokenCache();

const getValidToken = async (): Promise<string> => {
  const cached = tokenCache.get('access_token');
  if (cached) return cached;

  const tokens = await refreshTokens();
  tokenCache.set('access_token', tokens.accessToken, tokens.expiresIn);

  return tokens.accessToken;
};
```

#### Connection Pooling

```typescript
// Database connection optimization
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20, // Maximum connections
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
  maxUses: 7500, // Close connection after 7500 uses
});

// Optimized query execution
export const executeQuery = async (query: string, params: any[] = []) => {
  const client = await pool.connect();
  try {
    const result = await client.query(query, params);
    return result;
  } finally {
    client.release();
  }
};
```

---

## 🎯 Kesimpulan

Sistem Customer OAuth Authentication ini dirancang dengan standar FAANG untuk memberikan:

✅ **Security Terdepan** - PKCE, JWT dengan jose, rate limiting, CSRF protection
✅ **User Experience Optimal** - Seamless OAuth flow, automatic token refresh, biometric support
✅ **Developer Friendly** - Comprehensive documentation, TypeScript support, testing strategies
✅ **Production Ready** - Error handling, monitoring, performance optimization

**Next Steps:**
1. Implement monitoring dan analytics
2. Add support untuk additional OAuth providers
3. Implement advanced security features (device fingerprinting, risk scoring)
4. Add offline capability untuk mobile apps

Dengan dokumentasi ini, tim development dapat mengintegrasikan sistem autentikasi dengan confidence dan mengikuti best practices yang sudah terbukti di industri! 🚀

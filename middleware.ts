import { NextRequest, NextResponse } from 'next/server';
import { applySecurityHeaders, validateRequestForXSS } from '@/lib/middleware/security-headers';

/**
 * Next.js Middleware for Security Headers and XSS Protection
 * Applies comprehensive security headers to all requests
 */
export function middleware(request: NextRequest) {
  // Validate request for potential XSS attempts
  const xssValidation = validateRequestForXSS(request);
  
  if (!xssValidation.isValid) {
    console.warn('Potential XSS attempt detected:', {
      url: request.url,
      issues: xssValidation.issues,
      userAgent: request.headers.get('user-agent'),
      ip: request.ip || request.headers.get('x-forwarded-for')
    });
    
    // Block suspicious requests
    return NextResponse.json(
      { 
        success: false, 
        error: 'Request blocked for security reasons',
        errorCode: 'SECURITY_VIOLATION'
      },
      { status: 403 }
    );
  }

  // Continue with the request
  const response = NextResponse.next();
  
  // Apply security headers to all responses
  return applySecurityHeaders(response);
}

/**
 * Configure which paths the middleware should run on
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};

import React, { useCallback } from 'react';
import { usePackagePricing } from '@/lib/hooks/usePackagePricing';
import { PackagePricingCard } from './PackagePricingCard';
import { PackagePricingSkeleton } from './PackagePricingSkeleton';
import { PackagePricingEmpty } from './PackagePricingEmpty';
import { ErrorBoundary } from '@/components/ui/ErrorBoundary';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface PackagePricingPageProps {
  className?: string;
}

export function PackagePricingPage({ className = '' }: PackagePricingPageProps) {
  const {
    packages,
    loading,
    error,
    total,
    refetch,
    reset,
  } = usePackagePricing({
    enabled: true,
  });

  const handleSelectPackage = useCallback((packageId: string) => {
    const selectedPackage = packages.find(pkg => pkg.id === packageId);
    if (selectedPackage) {
      toast.success(`Package "${selectedPackage.packageName}" selected!`);
      console.log('Selected package:', selectedPackage);
      // Here you would typically navigate to checkout or booking flow
    }
  }, [packages]);

  const handleRefresh = useCallback(() => {
    refetch();
    toast.success('Package pricing refreshed');
  }, [refetch]);

  const handleClearFilters = useCallback(() => {
    reset();
  }, [reset]);

  // Determine which package should be recommended (middle price range or most credits)
  const getRecommendedPackageId = () => {
    if (packages.length === 0) return null;
    
    // Sort by credit amount descending, then by price ascending
    const sorted = [...packages].sort((a, b) => {
      if (b.creditAmount !== a.creditAmount) {
        return b.creditAmount - a.creditAmount;
      }
      return a.price - b.price;
    });
    
    // Return the package with best value (high credits, reasonable price)
    return sorted[0]?.id || null;
  };

  const recommendedPackageId = getRecommendedPackageId();

  return (
    <ErrorBoundary>
      <div className={cn("min-h-screen bg-white", className)}>
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-gray-900">
              Choose Your Perfect Package
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Select from our carefully crafted wellness packages designed to fit your lifestyle and fitness goals.
            </p>
          </div>

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-center space-x-3">
                <AlertCircle className="h-6 w-6 text-red-600" />
                <div>
                  <h3 className="text-lg font-semibold text-red-900">
                    Unable to load packages
                  </h3>
                  <p className="text-red-700 mt-1">{error}</p>
                </div>
              </div>
              <Button
                onClick={handleRefresh}
                className="mt-4 bg-red-600 hover:bg-red-700 text-white"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          )}

          {/* Loading State */}
          {loading && packages.length === 0 && (
            <div className="space-y-6">
              <div className="text-center">
                <div className="inline-flex items-center space-x-2 text-teal-600">
                  <RefreshCw className="h-5 w-5 animate-spin" />
                  <span className="text-lg font-medium">Loading packages...</span>
                </div>
              </div>
              <PackagePricingSkeleton count={3} />
            </div>
          )}

          {/* Empty State */}
          {!loading && packages.length === 0 && !error && (
            <PackagePricingEmpty
              hasFilters={false}
              onRefresh={handleRefresh}
            />
          )}

          {/* Package Grid */}
          {packages.length > 0 && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  {total} package{total !== 1 ? 's' : ''} available
                </div>
                <Button
                  onClick={handleRefresh}
                  variant="outline"
                  size="sm"
                  className="border-teal-600 text-teal-600 hover:bg-teal-50"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {packages.map((pkg) => (
                  <PackagePricingCard
                    key={pkg.id}
                    package={pkg}
                    isRecommended={pkg.id === recommendedPackageId}
                    onSelect={handleSelectPackage}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Additional Information */}
          {packages.length > 0 && (
            <div className="bg-teal-50 border border-teal-200 rounded-lg p-6 mt-8">
              <h3 className="text-lg font-semibold text-teal-900 mb-2">
                Need help choosing?
              </h3>
              <p className="text-teal-700 mb-4">
                Our wellness experts are here to help you find the perfect package for your fitness journey.
              </p>
              <Button
                variant="outline"
                className="border-teal-600 text-teal-600 hover:bg-teal-100"
              >
                Contact Support
              </Button>
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  );
}

import React from 'react';
import { Button } from '@/components/ui/button';
import { Package, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PackagePricingEmptyProps {
  hasFilters?: boolean;
  onRefresh?: () => void;
  onClearFilters?: () => void;
  className?: string;
}

export function PackagePricingEmpty({ 
  hasFilters = false, 
  onRefresh, 
  onClearFilters,
  className 
}: PackagePricingEmptyProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center py-16 px-8 text-center",
      "bg-white rounded-lg border border-gray-200 shadow-sm",
      className
    )}>
      <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mb-6">
        <Package className="h-8 w-8 text-teal-600" />
      </div>
      
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        {hasFilters ? 'No packages match your criteria' : 'No packages available'}
      </h3>
      
      <p className="text-gray-600 mb-6 max-w-md">
        {hasFilters
          ? 'Try adjusting your filters or search terms to find the perfect package for you.'
          : 'We\'re currently updating our package offerings. Please check back soon or contact us for more information.'
        }
      </p>
      
      <div className="flex flex-col sm:flex-row gap-3">
        {hasFilters && onClearFilters && (
          <Button
            onClick={onClearFilters}
            variant="outline"
            className="border-teal-600 text-teal-600 hover:bg-teal-50"
          >
            Clear Filters
          </Button>
        )}
        
        {onRefresh && (
          <Button
            onClick={onRefresh}
            className="bg-teal-600 hover:bg-teal-700 text-white"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>
    </div>
  );
}

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface PackagePricingSkeletonProps {
  count?: number;
  className?: string;
}

function PackagePricingCardSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn("h-full", className)}>
      <CardHeader className="text-center pb-4">
        {/* Badge skeleton */}
        <div className="flex justify-center mb-2">
          <Skeleton className="h-6 w-24 rounded-full" />
        </div>
        
        {/* Title skeleton */}
        <Skeleton className="h-6 w-3/4 mx-auto mb-2" />
        
        {/* Description skeleton */}
        <Skeleton className="h-4 w-full mb-4" />
        <Skeleton className="h-4 w-2/3 mx-auto" />
        
        {/* Price skeleton */}
        <div className="mt-4">
          <Skeleton className="h-8 w-32 mx-auto mb-1" />
          <Skeleton className="h-4 w-24 mx-auto" />
        </div>
      </CardHeader>

      <CardContent className="flex-1">
        {/* Features list skeleton */}
        <div className="space-y-3">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-start space-x-3">
              <Skeleton className="h-4 w-4 rounded-full mt-0.5 flex-shrink-0" />
              <Skeleton className={cn(
                "h-4",
                index === 0 ? "w-3/4" : 
                index === 1 ? "w-full" :
                index === 2 ? "w-2/3" :
                index === 3 ? "w-5/6" : "w-1/2"
              )} />
            </div>
          ))}
        </div>
      </CardContent>

      <CardFooter className="pt-6">
        <Skeleton className="h-12 w-full rounded-md" />
      </CardFooter>
    </Card>
  );
}

export function PackagePricingSkeleton({ count = 3, className }: PackagePricingSkeletonProps) {
  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6", className)}>
      {Array.from({ length: count }).map((_, index) => (
        <PackagePricingCardSkeleton key={index} />
      ))}
    </div>
  );
}
